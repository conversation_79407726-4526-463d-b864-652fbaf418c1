
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type Language = 'ar' | 'en';

export interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string>) => string;
  isRTL: boolean;
}

export const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguage] = useState<Language>(() => {
    const saved = localStorage.getItem('zenith-language');
    return (saved as Language) || 'ar';
  });

  const isRTL = language === 'ar';

  useEffect(() => {
    localStorage.setItem('zenith-language', language);
    document.documentElement.setAttribute('lang', language);
    document.documentElement.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
  }, [language, isRTL]);

  const t = (key: string, params?: Record<string, string>): string => {
    const translations = language === 'ar' ? arTranslations : enTranslations;
    let value = translations[key] || key;
    
    if (params) {
      Object.keys(params).forEach(param => {
        value = value.replace(`{{${param}}}`, params[param]);
      });
    }
    
    return value;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

const arTranslations: Record<string, string> = {
  // Navigation
  'nav.dashboard': 'لوحة التحكم',
  'nav.tasks': 'مهامي',
  'nav.projects': 'المشاريع',
  'nav.notes': 'الملاحظات',
  'nav.analytics': 'التحليلات',
  'nav.settings': 'الإعدادات',
  
  // Dashboard
  'dashboard.title': 'لوحة التحكم',
  'dashboard.welcome': 'مرحباً بك في Zenith Flow',
  'dashboard.subtitle': 'إنتاجيتك المثلى تبدأ من هنا',
  'dashboard.todayTasks': 'مهام اليوم',
  'dashboard.completedTasks': 'المهام المكتملة',
  'dashboard.totalProjects': 'إجمالي المشاريع',
  'dashboard.productivity': 'الإنتاجية',
  
  // Tasks
  'tasks.title': 'إدارة المهام',
  'tasks.description': 'نظم مهامك وحقق أهدافك',
  'tasks.newTask': 'مهمة جديدة',
  'tasks.priority.high': 'عالية',
  'tasks.priority.medium': 'متوسطة',
  'tasks.priority.low': 'منخفضة',
  'tasks.status.todo': 'قيد الانتظار',
  'tasks.status.inProgress': 'قيد التنفيذ',
  'tasks.status.completed': 'مكتملة',
  'tasks.status.cancelled': 'ملغية',
  'tasks.form.title': 'عنوان المهمة',
  'tasks.titlePlaceholder': 'أدخل عنوان المهمة...',
  'tasks.validation.titleRequired': 'عنوان المهمة مطلوب',
  'tasks.validation.titleTooLong': 'العنوان طويل جداً',
  'tasks.form.description': 'الوصف',
  'tasks.descriptionPlaceholder': 'اكتب وصف المهمة هنا...',
  'tasks.priority': 'الأولوية',
  'tasks.status': 'الحالة',
  'tasks.project': 'المشروع',
  'tasks.noProject': 'بدون مشروع',
  'tasks.assignedTo': 'مُكلف إلى',
  'tasks.assignedToPlaceholder': 'اسم المُكلف...',
  'tasks.estimatedHours': 'الساعات المقدرة',
  'tasks.actualHours': 'الساعات الفعلية',
  'tasks.tags': 'العلامات',
  'tasks.addTag': 'إضافة علامة...',
  'tasks.edit': 'تعديل المهمة',
  'tasks.delete': 'حذف المهمة',
  'tasks.confirmDelete': 'هل أنت متأكد من حذف هذه المهمة؟',
  'tasks.confirmDeleteDescription': 'لا يمكن التراجع عن هذا الإجراء.',
  'tasks.confirmBulkDelete': 'تأكيد حذف المهام',
  'tasks.confirmBulkDeleteDescription': 'هل أنت متأكد من حذف المهام المحددة؟ لا يمكن التراجع عن هذا الإجراء.',
  'tasks.dueDate': 'تاريخ الاستحقاق',
  'tasks.overdue': 'متأخرة',
  'tasks.today': 'اليوم',
  'tasks.tomorrow': 'غداً',
  'tasks.noTasks': 'لا توجد مهام',
  'tasks.noTasksDescription': 'ابدأ بإنشاء مهمتك الأولى',
  'tasks.subtitle': 'نظم مهامك وحقق أهدافك',
  'tasks.status.all': 'الكل',
  'tasks.taskDetails': 'تفاصيل المهمة',
  'tasks.notFound': 'المهمة غير موجودة',
  'tasks.notFoundDescription': 'لم يتم العثور على المهمة المطلوبة',
  'tasks.markComplete': 'تحديد كمكتملة',
  'tasks.markIncomplete': 'تحديد كغير مكتملة',
  'tasks.details': 'التفاصيل',
  'tasks.dates': 'التواريخ',
  'tasks.created': 'تاريخ الإنشاء',
  'tasks.lastModified': 'آخر تعديل',
  'tasks.completedAt': 'تاريخ الإكمال',
  'tasks.viewDetails': 'عرض التفاصيل',
  
  // Projects
  'projects.title': 'المشاريع',
  'projects.description': 'نظم وتتبع تقدم مشاريعك',
  'projects.allProjects': 'جميع المشاريع',
  'projects.noProjects': 'لا توجد مشاريع',
  'projects.noProjectsDescription': 'ابدأ بإنشاء مشروعك الأول',
  'projects.createFirst': 'إنشاء أول مشروع',
  'projects.newProject': 'مشروع جديد',
  'projects.progress': 'التقدم',
  'projects.tasks': 'المهام',
  'projects.team': 'الفريق',
  'projects.deadline': 'الموعد النهائي',
  'projects.members': 'عضو',
  'projects.daysLeft': 'يوم متبقي',
  'projects.daysOverdue': 'يوم متأخر',
  'projects.dueToday': 'مستحق اليوم',
  'projects.status.active': 'نشط',
  'projects.status.completed': 'مكتمل',
  'projects.status.onHold': 'معلق',
  'projects.status.planning': 'تخطيط',
  'projects.status.cancelled': 'ملغي',
  'projects.priority.high': 'عالية',
  'projects.priority.medium': 'متوسطة',
  'projects.priority.low': 'منخفضة',
  'projects.name': 'اسم المشروع',
  'projects.namePlaceholder': 'أدخل اسم المشروع...',
  'projects.validation.nameRequired': 'اسم المشروع مطلوب',
  'projects.validation.nameTooLong': 'اسم المشروع طويل جداً',
  'projects.form.description': 'الوصف',
  'projects.descriptionPlaceholder': 'اكتب وصف المشروع هنا...',
  'projects.status': 'الحالة',
  'projects.priority': 'الأولوية',
  'projects.startDate': 'تاريخ البداية',
  'projects.endDate': 'تاريخ النهاية',
  'projects.form.deadline': 'الموعد النهائي',
  'projects.form.progress': 'التقدم',
  'projects.budget': 'الميزانية',
  'projects.client': 'العميل',
  'projects.clientPlaceholder': 'اسم العميل...',
  'projects.color': 'اللون',
  'projects.teamMembers': 'أعضاء الفريق',
  'projects.addMember': 'إضافة عضو...',
  'projects.tags': 'العلامات',
  'projects.addTag': 'إضافة علامة...',
  'projects.edit': 'تعديل المشروع',
  'projects.delete': 'حذف المشروع',
  'projects.confirmDelete': 'هل أنت متأكد من حذف هذا المشروع؟',
  'projects.confirmDeleteDescription': 'سيتم حذف جميع المهام المرتبطة بهذا المشروع أيضاً.',
  'projects.confirmBulkDelete': 'تأكيد حذف المشاريع',
  'projects.confirmBulkDeleteDescription': 'هل أنت متأكد من حذف المشاريع المحددة؟ سيتم حذف جميع المهام المرتبطة بها أيضاً.',
  'projects.createNew': 'إنشاء مشروع جديد',
  'projects.startOrganizing': 'ابدأ في تنظيم فكرتك الكبيرة القادمة',
  'projects.empty.title': 'لا توجد مشاريع',
  'projects.empty.description': 'ابدأ بإنشاء مشروعك الأول لتنظيم أعمالك',
  'projects.projectDetails': 'تفاصيل المشروع',
  'projects.notFound': 'المشروع غير موجود',
  'projects.notFoundDescription': 'لم يتم العثور على المشروع المطلوب',
  'projects.loadError': 'حدث خطأ أثناء تحميل المشروع',
  'projects.statistics': 'الإحصائيات',
  'projects.totalTasks': 'إجمالي المهام',
  'projects.completedTasks': 'المهام المكتملة',
  'projects.remainingTasks': 'المهام المتبقية',
  'projects.dates': 'التواريخ',
  'projects.created': 'تاريخ الإنشاء',
  'projects.viewDetails': 'عرض التفاصيل',
  
  // Notes
  'notes.title': 'الملاحظات',
  'notes.description': 'احفظ أفكارك ونظمها',
  'notes.newNote': 'ملاحظة جديدة',
  'notes.allNotes': 'جميع الملاحظات',
  'notes.noNotes': 'لا توجد ملاحظات',
  'notes.noNotesDescription': 'ابدأ بإنشاء ملاحظتك الأولى',
  'notes.createFirst': 'إنشاء أول ملاحظة',
  'notes.lastModified': 'آخر تعديل',
  'notes.wordCount': 'كلمة',
  'notes.titlePlaceholder': 'أدخل عنوان الملاحظة...',
  'notes.validation.titleRequired': 'عنوان الملاحظة مطلوب',
  'notes.validation.titleTooLong': 'العنوان طويل جداً',
  'notes.validation.contentRequired': 'محتوى الملاحظة مطلوب',
  'notes.contentPlaceholder': 'اكتب محتوى الملاحظة هنا...',
  'notes.project': 'المشروع',
  'notes.noProject': 'بدون مشروع',
  'notes.tags': 'العلامات',
  'notes.addTag': 'إضافة علامة...',
  'notes.color': 'اللون',
  'notes.pinNote': 'تثبيت الملاحظة',
  'notes.edit': 'تعديل',
  'notes.delete': 'حذف',
  'notes.pin': 'تثبيت',
  'notes.unpin': 'إلغاء التثبيت',
  'notes.confirmDelete': 'هل أنت متأكد من حذف هذه الملاحظة؟',
  'notes.confirmDeleteDescription': 'لا يمكن التراجع عن هذا الإجراء.',
  'notes.confirmBulkDelete': 'تأكيد حذف الملاحظات',
  'notes.confirmBulkDeleteDescription': 'هل أنت متأكد من حذف الملاحظات المحددة؟ لا يمكن التراجع عن هذا الإجراء.',
  'notes.deleteSuccess': 'تم حذف الملاحظة بنجاح',
  'notes.deleteError': 'فشل في حذف الملاحظة',
  'notes.empty.title': 'لا توجد ملاحظات',
  'notes.empty.description': 'ابدأ بإنشاء ملاحظتك الأولى لحفظ أفكارك',
  'notes.noteDetails': 'تفاصيل الملاحظة',
  'notes.notFound': 'الملاحظة غير موجودة',
  'notes.notFoundDescription': 'لم يتم العثور على الملاحظة المطلوبة',
  'notes.created': 'تاريخ الإنشاء',
  'notes.viewDetails': 'عرض التفاصيل',
  
  // Analytics
  'analytics.title': 'التحليلات والتقارير',
  'analytics.description': 'تتبع وحلل إنتاجيتك',
  'analytics.weeklyProgress': 'التقدم الأسبوعي',
  'analytics.recentActivity': 'النشاط الأخير',
  'analytics.focusTime': 'وقت التركيز',
  'analytics.weeklyGoal': 'الهدف الأسبوعي',
  'analytics.overview': 'نظرة عامة',
  'analytics.tasksCompleted': 'المهام المكتملة',
  'analytics.timeSpent': 'الوقت المستغرق',
  'analytics.productivity': 'الإنتاجية',
  'analytics.projectsActive': 'المشاريع النشطة',
  'analytics.thisWeek': 'هذا الأسبوع',
  'analytics.thisMonth': 'هذا الشهر',
  'analytics.performance': 'الأداء',
  'analytics.trends': 'الاتجاهات',
  'analytics.minutes': 'دقيقة',
  'analytics.hours': 'ساعة',
  
  // Settings
  'settings.title': 'الإعدادات',
  'settings.description': 'خصص تجربتك',
  'settings.appearance': 'المظهر',
  'settings.notifications': 'الإشعارات',
  'settings.privacy': 'الخصوصية',
  'settings.dataManagement': 'إدارة البيانات',
  'settings.about': 'حول التطبيق',
  'settings.language': 'اللغة',
  'settings.theme': 'السمة',
  'settings.light': 'فاتح',
  'settings.dark': 'داكن',
  'settings.system': 'النظام',
  'settings.taskReminders': 'تذكيرات المهام',
  'settings.focusBreaks': 'فترات الراحة',
  'settings.analytics': 'التحليلات',
  'settings.dataSharing': 'مشاركة البيانات',
  'settings.exportData': 'تصدير البيانات',
  'settings.exportDescription': 'تصدير جميع بياناتك',
  'settings.clearData': 'مسح البيانات',
  'settings.clearDescription': 'حذف جميع البيانات نهائياً',
  'settings.resetData': 'إعادة تعيين جميع البيانات',
  'settings.resetDescription': 'مسح جميع البيانات والعودة للحالة الأولية',
  'settings.resetConfirmTitle': 'إعادة تعيين جميع البيانات؟',
  'settings.resetConfirmMessage': 'سيؤدي هذا إلى حذف جميع مهامك ومشاريعك وملاحظاتك وبياناتك الأخرى نهائياً. لا يمكن التراجع عن هذا الإجراء.',
  'settings.resetConfirmWarning': 'هل أنت متأكد تماماً من أنك تريد المتابعة؟',
  'settings.resetPreserveSettings': 'الاحتفاظ بإعداداتي (السمة، اللغة)',
  'settings.resetInProgress': 'جاري إعادة تعيين البيانات...',
  'settings.resetSuccess': 'تم إعادة تعيين جميع البيانات بنجاح',
  'settings.resetError': 'فشل في إعادة تعيين البيانات',
  'settings.resetStep.preserveSettings': 'جاري الاحتفاظ بالإعدادات...',
  'settings.resetStep.clearCache': 'جاري مسح التخزين المؤقت...',
  'settings.resetStep.clearDatabase': 'جاري مسح قاعدة البيانات...',
  'settings.resetStep.clearStorage': 'جاري مسح التخزين...',
  'settings.resetStep.restoreSettings': 'جاري استعادة الإعدادات...',
  'settings.dataStats': 'إحصائيات البيانات',
  'settings.dataStatsDescription': 'استخدام البيانات الحالي في تطبيقك',
  'settings.export': 'تصدير',
  'settings.clear': 'مسح',
  'settings.reset': 'إعادة تعيين',
  'settings.fontSize': 'حجم الخط',
  
  // Search
  'search.placeholder': 'ابحث في مهامك ومشاريعك...',
  'search.voice': 'البحث الصوتي',
  'search.results': 'نتائج البحث',
  
  // AI Assistant
  'ai.title': 'المساعد الذكي',
  'ai.placeholder': 'اسألني أي شيء عن مهامك ومشاريعك...',
  'ai.thinking': 'أفكر...',
  'ai.suggestions': 'اقتراحات',
  'ai.createTask': 'إنشاء مهمة جديدة',
  'ai.analyzeProductivity': 'تحليل الإنتاجية',
  'ai.organizeDay': 'تنظيم اليوم',
  
  // Focus Mode
  'focus.title': 'وضع التركيز',
  'focus.description': 'ركز على ما يهم حقاً',
  'focus.ready': 'جاهز للتركيز؟',
  'focus.readyDescription': 'ابدأ جلسة تركيز لتحسين إنتاجيتك',
  'focus.start': 'بدء التركيز',
  'focus.pause': 'إيقاف مؤقت',
  'focus.stop': 'إيقاف',
  'focus.pomodoroTimer': 'مؤقت البومودورو',
  'focus.workSession': 'جلسة عمل',
  'focus.shortBreak': 'استراحة قصيرة',
  'focus.longBreak': 'استراحة طويلة',
  'focus.ambientSounds': 'أصوات البيئة المحيطة',
  'focus.rain': 'المطر',
  'focus.ocean': 'المحيط',
  'focus.forest': 'الغابة',
  'focus.cafe': 'المقهى',
  
  // Common
  'common.save': 'حفظ',
  'common.cancel': 'إلغاء',
  'common.delete': 'حذف',
  'common.edit': 'تعديل',
  'common.create': 'إنشاء',
  'common.update': 'تحديث',
  'common.search': 'بحث',
  'common.filter': 'فلترة',
  'common.sort': 'ترتيب',
  'common.close': 'إغلاق',
  'common.open': 'فتح',
  'common.loading': 'جاري التحميل...',
  'common.error': 'خطأ',
  'common.retry': 'إعادة المحاولة',
  'common.success': 'نجح',
  'common.confirm': 'تأكيد',
  'common.back': 'رجوع',
  'common.next': 'التالي',
  'common.previous': 'السابق',
  'common.all': 'الكل',
  'common.none': 'لا شيء',
  'common.yes': 'نعم',
  'common.no': 'لا',
  'common.warning': 'تحذير',
  'common.saving': 'جاري الحفظ...',
  'common.select': 'تحديد',
  'common.selectAll': 'تحديد الكل',
  'common.deselectAll': 'إلغاء تحديد الكل',
  'common.selectedItems': 'تم تحديد {{count}} عنصر',
  'common.selectItems': 'حدد العناصر',
  'common.deleteSelected': 'حذف المحدد',
  'common.confirmBulkDelete': 'تأكيد الحذف الجماعي',
  'common.confirmBulkDeleteDescription': 'هل أنت متأكد من حذف {{count}} عنصر؟ لا يمكن التراجع عن هذا الإجراء.',
};

const enTranslations: Record<string, string> = {
  // Navigation
  'nav.dashboard': 'Dashboard',
  'nav.tasks': 'My Tasks',
  'nav.projects': 'Projects',
  'nav.notes': 'Notes',
  'nav.analytics': 'Analytics',
  'nav.settings': 'Settings',
  
  // Dashboard
  'dashboard.title': 'Dashboard',
  'dashboard.welcome': 'Welcome to Zenith Flow',
  'dashboard.subtitle': 'Your peak productivity starts here',
  'dashboard.todayTasks': "Today's Tasks",
  'dashboard.completedTasks': 'Completed Tasks',
  'dashboard.totalProjects': 'Total Projects',
  'dashboard.productivity': 'Productivity',
  
  // Tasks
  'tasks.title': 'Task Management',
  'tasks.description': 'Organize your tasks and achieve your goals',
  'tasks.newTask': 'New Task',
  'tasks.priority.high': 'High',
  'tasks.priority.medium': 'Medium',
  'tasks.priority.low': 'Low',
  'tasks.status.todo': 'To Do',
  'tasks.status.inProgress': 'In Progress',
  'tasks.status.completed': 'Completed',
  'tasks.status.cancelled': 'Cancelled',
  'tasks.form.title': 'Task Title',
  'tasks.titlePlaceholder': 'Enter task title...',
  'tasks.validation.titleRequired': 'Task title is required',
  'tasks.validation.titleTooLong': 'Title is too long',
  'tasks.form.description': 'Description',
  'tasks.descriptionPlaceholder': 'Write task description here...',
  'tasks.priority': 'Priority',
  'tasks.status': 'Status',
  'tasks.project': 'Project',
  'tasks.noProject': 'No Project',
  'tasks.assignedTo': 'Assigned To',
  'tasks.assignedToPlaceholder': 'Assignee name...',
  'tasks.estimatedHours': 'Estimated Hours',
  'tasks.actualHours': 'Actual Hours',
  'tasks.tags': 'Tags',
  'tasks.addTag': 'Add tag...',
  'tasks.edit': 'Edit Task',
  'tasks.delete': 'Delete Task',
  'tasks.confirmDelete': 'Are you sure you want to delete this task?',
  'tasks.confirmDeleteDescription': 'This action cannot be undone.',
  'tasks.confirmBulkDelete': 'Confirm Delete Tasks',
  'tasks.confirmBulkDeleteDescription': 'Are you sure you want to delete the selected tasks? This action cannot be undone.',
  'tasks.dueDate': 'Due Date',
  'tasks.overdue': 'Overdue',
  'tasks.today': 'Today',
  'tasks.tomorrow': 'Tomorrow',
  'tasks.noTasks': 'No Tasks',
  'tasks.noTasksDescription': 'Start by creating your first task',
  'tasks.subtitle': 'Organize your tasks and achieve your goals',
  'tasks.status.all': 'All',
  'tasks.taskDetails': 'Task Details',
  'tasks.notFound': 'Task Not Found',
  'tasks.notFoundDescription': 'The requested task could not be found',
  'tasks.markComplete': 'Mark Complete',
  'tasks.markIncomplete': 'Mark Incomplete',
  'tasks.details': 'Details',
  'tasks.dates': 'Dates',
  'tasks.created': 'Created',
  'tasks.lastModified': 'Last Modified',
  'tasks.completedAt': 'Completed At',
  'tasks.viewDetails': 'View Details',
  
  // Projects
  'projects.title': 'Projects',
  'projects.description': 'Organize and track your project progress',
  'projects.allProjects': 'All Projects',
  'projects.noProjects': 'No Projects',
  'projects.noProjectsDescription': 'Start by creating your first project',
  'projects.createFirst': 'Create First Project',
  'projects.newProject': 'New Project',
  'projects.progress': 'Progress',
  'projects.tasks': 'Tasks',
  'projects.team': 'Team',
  'projects.deadline': 'Deadline',
  'projects.members': 'members',
  'projects.daysLeft': 'days left',
  'projects.daysOverdue': 'days overdue',
  'projects.dueToday': 'Due today',
  'projects.status.active': 'Active',
  'projects.status.completed': 'Completed',
  'projects.status.onHold': 'On Hold',
  'projects.status.planning': 'Planning',
  'projects.status.cancelled': 'Cancelled',
  'projects.priority.high': 'High',
  'projects.priority.medium': 'Medium',
  'projects.priority.low': 'Low',
  'projects.name': 'Project Name',
  'projects.namePlaceholder': 'Enter project name...',
  'projects.validation.nameRequired': 'Project name is required',
  'projects.validation.nameTooLong': 'Project name is too long',
  'projects.form.description': 'Description',
  'projects.descriptionPlaceholder': 'Write project description here...',
  'projects.status': 'Status',
  'projects.priority': 'Priority',
  'projects.startDate': 'Start Date',
  'projects.endDate': 'End Date',
  'projects.form.deadline': 'Deadline',
  'projects.form.progress': 'Progress',
  'projects.budget': 'Budget',
  'projects.client': 'Client',
  'projects.clientPlaceholder': 'Client name...',
  'projects.color': 'Color',
  'projects.teamMembers': 'Team Members',
  'projects.addMember': 'Add member...',
  'projects.tags': 'Tags',
  'projects.addTag': 'Add tag...',
  'projects.edit': 'Edit Project',
  'projects.delete': 'Delete Project',
  'projects.confirmDelete': 'Are you sure you want to delete this project?',
  'projects.confirmDeleteDescription': 'All tasks associated with this project will also be deleted.',
  'projects.confirmBulkDelete': 'Confirm Delete Projects',
  'projects.confirmBulkDeleteDescription': 'Are you sure you want to delete the selected projects? All associated tasks will also be deleted.',
  'projects.createNew': 'Create New Project',
  'projects.startOrganizing': 'Start organizing your next big idea',
  'projects.empty.title': 'No Projects',
  'projects.empty.description': 'Start by creating your first project to organize your work',
  'projects.projectDetails': 'Project Details',
  'projects.notFound': 'Project Not Found',
  'projects.notFoundDescription': 'The requested project could not be found',
  'projects.loadError': 'An error occurred while loading the project',
  'projects.statistics': 'Statistics',
  'projects.totalTasks': 'Total Tasks',
  'projects.completedTasks': 'Completed Tasks',
  'projects.remainingTasks': 'Remaining Tasks',
  'projects.dates': 'Dates',
  'projects.created': 'Created',
  'projects.viewDetails': 'View Details',
  
  // Notes
  'notes.title': 'Notes',
  'notes.description': 'Capture and organize your thoughts',
  'notes.newNote': 'New Note',
  'notes.allNotes': 'All Notes',
  'notes.noNotes': 'No Notes',
  'notes.noNotesDescription': 'Start by creating your first note',
  'notes.createFirst': 'Create First Note',
  'notes.lastModified': 'Last modified',
  'notes.wordCount': 'words',
  'notes.titlePlaceholder': 'Enter note title...',
  'notes.validation.titleRequired': 'Note title is required',
  'notes.validation.titleTooLong': 'Title is too long',
  'notes.validation.contentRequired': 'Note content is required',
  'notes.contentPlaceholder': 'Write your note content here...',
  'notes.project': 'Project',
  'notes.noProject': 'No Project',
  'notes.tags': 'Tags',
  'notes.addTag': 'Add tag...',
  'notes.color': 'Color',
  'notes.pinNote': 'Pin Note',
  'notes.edit': 'Edit',
  'notes.delete': 'Delete',
  'notes.pin': 'Pin',
  'notes.unpin': 'Unpin',
  'notes.confirmDelete': 'Are you sure you want to delete this note?',
  'notes.confirmDeleteDescription': 'This action cannot be undone.',
  'notes.confirmBulkDelete': 'Confirm Delete Notes',
  'notes.confirmBulkDeleteDescription': 'Are you sure you want to delete the selected notes? This action cannot be undone.',
  'notes.deleteSuccess': 'Note deleted successfully',
  'notes.deleteError': 'Failed to delete note',
  'notes.empty.title': 'No Notes',
  'notes.empty.description': 'Start by creating your first note to capture your thoughts',
  'notes.noteDetails': 'Note Details',
  'notes.notFound': 'Note Not Found',
  'notes.notFoundDescription': 'The requested note could not be found',
  'notes.created': 'Created',
  'notes.viewDetails': 'View Details',
  
  // Analytics
  'analytics.title': 'Analytics & Reports',
  'analytics.description': 'Track and analyze your productivity',
  'analytics.weeklyProgress': 'Weekly Progress',
  'analytics.recentActivity': 'Recent Activity',
  'analytics.focusTime': 'Focus Time',
  'analytics.weeklyGoal': 'Weekly Goal',
  'analytics.overview': 'Overview',
  'analytics.tasksCompleted': 'Tasks Completed',
  'analytics.timeSpent': 'Time Spent',
  'analytics.productivity': 'Productivity',
  'analytics.projectsActive': 'Active Projects',
  'analytics.thisWeek': 'This Week',
  'analytics.thisMonth': 'This Month',
  'analytics.performance': 'Performance',
  'analytics.trends': 'Trends',
  'analytics.minutes': 'minutes',
  'analytics.hours': 'hours',
  
  // Settings
  'settings.title': 'Settings',
  'settings.description': 'Customize your experience',
  'settings.appearance': 'Appearance',
  'settings.notifications': 'Notifications',
  'settings.privacy': 'Privacy',
  'settings.dataManagement': 'Data Management',
  'settings.about': 'About',
  'settings.language': 'Language',
  'settings.theme': 'Theme',
  'settings.light': 'Light',
  'settings.dark': 'Dark',
  'settings.system': 'System',
  'settings.taskReminders': 'Task Reminders',
  'settings.focusBreaks': 'Focus Breaks',
  'settings.analytics': 'Analytics',
  'settings.dataSharing': 'Data Sharing',
  'settings.exportData': 'Export Data',
  'settings.exportDescription': 'Export all your data',
  'settings.clearData': 'Clear Data',
  'settings.clearDescription': 'Permanently delete all data',
  'settings.resetData': 'Reset All Data',
  'settings.resetDescription': 'Clear all data and return to initial state',
  'settings.resetConfirmTitle': 'Reset All Data?',
  'settings.resetConfirmMessage': 'This will permanently delete all your tasks, projects, notes, and other data. This action cannot be undone.',
  'settings.resetConfirmWarning': 'Are you absolutely sure you want to continue?',
  'settings.resetPreserveSettings': 'Preserve my settings (theme, language)',
  'settings.resetInProgress': 'Resetting data...',
  'settings.resetSuccess': 'All data has been successfully reset',
  'settings.resetError': 'Failed to reset data',
  'settings.resetStep.preserveSettings': 'Preserving settings...',
  'settings.resetStep.clearCache': 'Clearing cache...',
  'settings.resetStep.clearDatabase': 'Clearing database...',
  'settings.resetStep.clearStorage': 'Clearing storage...',
  'settings.resetStep.restoreSettings': 'Restoring settings...',
  'settings.dataStats': 'Data Statistics',
  'settings.dataStatsDescription': 'Current data usage in your application',
  'settings.export': 'Export',
  'settings.clear': 'Clear',
  'settings.reset': 'Reset',
  'settings.fontSize': 'Font Size',
  
  // Search
  'search.placeholder': 'Search your tasks and projects...',
  'search.voice': 'Voice Search',
  'search.results': 'Search Results',
  
  // AI Assistant
  'ai.title': 'AI Assistant',
  'ai.placeholder': 'Ask me anything about your tasks and projects...',
  'ai.thinking': 'Thinking...',
  'ai.suggestions': 'Suggestions',
  'ai.createTask': 'Create New Task',
  'ai.analyzeProductivity': 'Analyze Productivity',
  'ai.organizeDay': 'Organize Day',
  
  // Focus Mode
  'focus.title': 'Focus Mode',
  'focus.description': 'Focus on what truly matters',
  'focus.ready': 'Ready to Focus?',
  'focus.readyDescription': 'Start a focus session to boost your productivity',
  'focus.start': 'Start Focus',
  'focus.pause': 'Pause',
  'focus.stop': 'Stop',
  'focus.pomodoroTimer': 'Pomodoro Timer',
  'focus.workSession': 'Work Session',
  'focus.shortBreak': 'Short Break',
  'focus.longBreak': 'Long Break',
  'focus.ambientSounds': 'Ambient Sounds',
  'focus.rain': 'Rain',
  'focus.ocean': 'Ocean',
  'focus.forest': 'Forest',
  'focus.cafe': 'Cafe',
  
  // Common
  'common.save': 'Save',
  'common.cancel': 'Cancel',
  'common.delete': 'Delete',
  'common.edit': 'Edit',
  'common.create': 'Create',
  'common.update': 'Update',
  'common.search': 'Search',
  'common.filter': 'Filter',
  'common.sort': 'Sort',
  'common.close': 'Close',
  'common.open': 'Open',
  'common.loading': 'Loading...',
  'common.error': 'Error',
  'common.retry': 'Retry',
  'common.success': 'Success',
  'common.confirm': 'Confirm',
  'common.back': 'Back',
  'common.next': 'Next',
  'common.previous': 'Previous',
  'common.all': 'All',
  'common.none': 'None',
  'common.yes': 'Yes',
  'common.no': 'No',
  'common.warning': 'Warning',
  'common.saving': 'Saving...',
  'common.select': 'Select',
  'common.selectAll': 'Select All',
  'common.deselectAll': 'Deselect All',
  'common.selectedItems': '{{count}} items selected',
  'common.selectItems': 'Select items',
  'common.deleteSelected': 'Delete Selected',
  'common.confirmBulkDelete': 'Confirm Bulk Delete',
  'common.confirmBulkDeleteDescription': 'Are you sure you want to delete {{count}} items? This action cannot be undone.',
};

// Custom hook to use the language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
