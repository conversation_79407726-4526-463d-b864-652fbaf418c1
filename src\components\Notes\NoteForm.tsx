import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Save, FileText, Tag, Pin, Palette } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCreateNote, useUpdateNote, useProjects } from '@/hooks/useData';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { ResponsiveModal } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import type { Note } from '@/types/database';

// Form validation schema - will be created with proper translations
const createNoteSchema = (t: (key: string) => string) => z.object({
  title: z.string().min(1, t('notes.validation.titleRequired')).max(200, t('notes.validation.titleTooLong')),
  content: z.string().min(1, t('notes.validation.contentRequired')),
  tags: z.array(z.string()).default([]),
  isPinned: z.boolean().default(false),
  isArchived: z.boolean().default(false),
  color: z.string().optional(),
  projectId: z.string().optional(),
  taskId: z.string().optional(),
  attachments: z.array(z.string()).default([])
});

type NoteFormData = z.infer<ReturnType<typeof createNoteSchema>>;

interface NoteFormProps {
  isOpen: boolean;
  onClose: () => void;
  projectId?: string;
  taskId?: string;
  note?: Note; // For editing existing note
}

const NoteForm: React.FC<NoteFormProps> = ({
  isOpen,
  onClose,
  projectId,
  taskId,
  note
}) => {
  const { t, isRTL } = useLanguage();
  const { primary } = useColors();
  const [tagInput, setTagInput] = useState('');
  const [selectedColor, setSelectedColor] = useState<string>('');

  const isEditing = !!note;
  const createNoteMutation = useCreateNote();
  const updateNoteMutation = useUpdateNote();
  const { data: projectsResult } = useProjects({}, { page: 1, limit: 100 });
  const projects = projectsResult?.data || [];

  const noteSchema = React.useMemo(() => createNoteSchema(t), [t]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<NoteFormData>({
    resolver: zodResolver(noteSchema),
    defaultValues: {
      title: note?.title || '',
      content: note?.content || '',
      tags: note?.tags || [],
      isPinned: note?.isPinned || false,
      isArchived: note?.isArchived || false,
      color: note?.color || '',
      projectId: note?.projectId || projectId || '',
      taskId: note?.taskId || taskId || '',
      attachments: note?.attachments || []
    }
  });

  const watchedTags = watch('tags');
  const watchedIsPinned = watch('isPinned');

  // Initialize form with note data when editing
  useEffect(() => {
    if (note && isOpen) {
      setValue('title', note.title);
      setValue('content', note.content);
      setValue('tags', note.tags);
      setValue('isPinned', note.isPinned);
      setValue('isArchived', note.isArchived);
      setValue('color', note.color || '');
      setValue('projectId', note.projectId || '');
      setValue('taskId', note.taskId || '');
      setValue('attachments', note.attachments);
      setSelectedColor(note.color || '');
    }
  }, [note, isOpen, setValue]);

  const noteColors = [
    { name: 'Default', value: '', class: 'bg-background' },
    { name: 'Yellow', value: 'yellow', class: 'bg-yellow-100 dark:bg-yellow-900/20' },
    { name: 'Blue', value: 'blue', class: 'bg-blue-100 dark:bg-blue-900/20' },
    { name: 'Green', value: 'green', class: 'bg-green-100 dark:bg-green-900/20' },
    { name: 'Purple', value: 'purple', class: 'bg-purple-100 dark:bg-purple-900/20' },
    { name: 'Pink', value: 'pink', class: 'bg-pink-100 dark:bg-pink-900/20' },
    { name: 'Orange', value: 'orange', class: 'bg-orange-100 dark:bg-orange-900/20' },
  ];

  const handleAddTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      setValue('tags', [...watchedTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const onSubmit = async (data: NoteFormData) => {
    try {
      if (isEditing && note) {
        await updateNoteMutation.mutateAsync({
          id: note.id,
          updates: {
            ...data,
            color: selectedColor || undefined
          }
        });
      } else {
        await createNoteMutation.mutateAsync({
          ...data,
          color: selectedColor || undefined
        });
      }
      reset();
      setSelectedColor('');
      onClose();
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} note:`, error);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedColor('');
    setTagInput('');
    onClose();
  };

  const modalFooter = (
    <div className={cn(
      'flex items-center gap-3',
      isRTL ? 'flex-row-reverse' : 'flex-row'
    )}>
      <Button
        type="button"
        variant="outline"
        onClick={handleClose}
        disabled={isSubmitting || createNoteMutation.isPending || updateNoteMutation.isPending}
        className="flex-1 sm:flex-none touch-target focus-visible"
      >
        {t('common.cancel')}
      </Button>
      <Button
        type="submit"
        form="note-form"
        disabled={isSubmitting || createNoteMutation.isPending || updateNoteMutation.isPending}
        className="flex-1 sm:flex-none bg-zenith-gradient text-white hover:shadow-zenith-lg touch-target focus-visible"
      >
        <Save className={cn("w-4 h-4", isRTL ? "ml-2" : "mr-2")} />
        {(isSubmitting || createNoteMutation.isPending || updateNoteMutation.isPending)
          ? t('common.saving')
          : isEditing
            ? t('notes.updateNote')
            : t('notes.createNote')
        }
      </Button>
    </div>
  );

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? t('notes.editNote') : t('notes.newNote')}
      description={isEditing ? t('notes.editDescription') : t('notes.newDescription')}
      size="lg"
      footer={modalFooter}
      className={cn(
        selectedColor && noteColors.find(c => c.value === selectedColor)?.class
      )}
    >

      <form
        id="note-form"
        onSubmit={handleSubmit(onSubmit)}
        className="p-4 sm:p-6 space-y-6"
      >
          {/* Title */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('notes.title')}
            </label>
            <input
              {...register('title')}
              type="text"
              placeholder={t('notes.titlePlaceholder')}
              className={cn(
                "w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20",
                errors.title && "border-destructive"
              )}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title.message}</p>
            )}
          </div>

          {/* Content */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('notes.content')}
            </label>
            <textarea
              {...register('content')}
              rows={8}
              placeholder={t('notes.contentPlaceholder')}
              className={cn(
                "w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none",
                errors.content && "border-destructive"
              )}
            />
            {errors.content && (
              <p className="text-sm text-destructive">{errors.content.message}</p>
            )}
          </div>

          {/* Project Selection */}
          {projects.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                {t('notes.project')}
              </label>
              <select
                {...register('projectId')}
                className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="">{t('notes.noProject')}</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Tags */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('notes.tags')}
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={t('notes.addTag')}
                className="flex-1 px-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
              <button
                type="button"
                onClick={handleAddTag}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Tag className="w-4 h-4" />
              </button>
            </div>
            {watchedTags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {watchedTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-3 py-1 bg-muted text-muted-foreground rounded-full text-sm"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="hover:text-destructive"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Color Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('notes.color')}
            </label>
            <div className="flex gap-2 flex-wrap">
              {noteColors.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => {
                    setSelectedColor(color.value);
                    setValue('color', color.value);
                  }}
                  className={cn(
                    "w-8 h-8 rounded-full border-2 transition-all",
                    color.class,
                    selectedColor === color.value
                      ? "border-primary scale-110"
                      : "border-border hover:border-primary/50"
                  )}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                {...register('isPinned')}
                type="checkbox"
                className="sr-only"
              />
              <div className={cn(
                "flex items-center justify-center w-5 h-5 border-2 rounded transition-colors",
                watchedIsPinned
                  ? "bg-primary border-primary text-primary-foreground"
                  : "border-border hover:border-primary/50"
              )}>
                {watchedIsPinned && <Pin className="w-3 h-3" />}
              </div>
              <span className="text-sm">{t('notes.pinNote')}</span>
            </label>
          </div>
        </form>
    </ResponsiveModal>
  );
};

export default NoteForm;
